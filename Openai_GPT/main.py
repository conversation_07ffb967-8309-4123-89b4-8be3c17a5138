from fastapi import FastAP<PERSON>, UploadFile, File, Form
from pydantic import BaseModel
from langchain_community.llms import Ollama
import tempfile
import docx
import fitz
import requests
import os
import base64
import json
import re

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str

llm = Ollama(model="gemma3:4b", base_url="http://localhost:11434")
OLLAMA_API_URL = "http://localhost:11434/api/generate"

def extract_text_from_pdf(file_path: str) -> str:
    text = ""
    with fitz.open(file_path) as pdf:
        for page in pdf:
            text += page.get_text()
    return text

def extract_text_from_docx(file_path: str) -> str:
    doc = docx.Document(file_path)
    return "\n".join([para.text for para in doc.paragraphs])

def send_image_to_ollama(prompt: str, image_path: str):
    try:
        with open(image_path, "rb") as img_file:
            img_data = base64.b64encode(img_file.read()).decode("utf-8")
        response = requests.post(
            OLLAMA_API_URL,
            json={
                "model": "gemma3:4b",
                "prompt": prompt,
                "images": [img_data],
                "stream": True
            },
            stream=True
        )
        full_response = ""
        for line in response.iter_lines():
            if line:
                chunk = json.loads(line.decode("utf-8"))
                full_response += chunk.get("response", "")
                if chunk.get("done", False):
                    break
        cleaned_response = re.sub(r'```json\n|\n|```', '', full_response).strip()
        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            return cleaned_response
    except Exception as e:
        return str(e)

def try_parse_json(text):
    if isinstance(text, (dict, list)):
        return {"raw": text, "parsed": text}
    if not isinstance(text, str):
        return {"raw": str(text), "parsed": None}
    match = re.search(r'\{.*\}', text, re.DOTALL)
    if match:
        json_str = match.group(0)
        try:
            parsed = json.loads(json_str)
            return parsed
        except json.JSONDecodeError:
            return {"parsed": None}
    return {"parsed": None}

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        response = llm(request.prompt)
        return {"response": try_parse_json(response)}
    except Exception as e:
        return {"error": str(e)}

@app.post("/generate-from-file")
async def generate_from_file(prompt: str = Form(...), file: UploadFile = File(...)):
    try:
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(await file.read())
            tmp_path = tmp.name

        filename = file.filename.lower()
        if filename.endswith(".pdf"):
            file_text = extract_text_from_pdf(tmp_path)
            full_input = f"{prompt}\n\nDocument Content:\n{file_text}"
            response = llm(full_input)
        elif filename.endswith(".docx"):
            file_text = extract_text_from_docx(tmp_path)
            full_input = f"{prompt}\n\nDocument Content:\n{file_text}"
            response = llm(full_input)
        elif filename.endswith((".jpg", ".jpeg", ".png")):
            response = send_image_to_ollama(prompt, tmp_path)
        else:
            return {"error": "Unsupported file format. Only PDF, DOCX, JPG, and PNG allowed."}

        return {"response": try_parse_json(response)}
    except Exception as e:
        return {"error": str(e)}
    finally:
        if 'tmp_path' in locals():
            os.unlink(tmp_path)
